// Background Script for YouTube Summary Extension

// 存储配置
let config = {
  openaiApiKey: '',
  geminiApiKey: '',
  deepseekApiKey: '',
  preferredModel: 'gpt-3.5-turbo',
  summaryTemplate: '',
  mindmapTemplate: ''
};

// 从存储中加载配置
chrome.storage.sync.get([
  'openaiApiKey', 
  'geminiApiKey', 
  'deepseekApiKey', 
  'preferredModel',
  'summaryTemplate',
  'mindmapTemplate'
], (result) => {
  Object.assign(config, result);
});

// 监听消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'generateSummary') {
    generateSummary(request.captions, request.videoId)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({error: error.message}));
    return true; // 表示异步响应
  }

  if (request.action === 'saveConfig') {
    Object.assign(config, request.config);
    chrome.storage.sync.set(request.config, () => {
      sendResponse({status: 'success'});
    });
    return true; // 表示异步响应
  }

  if (request.action === 'showSummary') {
    // 存储摘要数据供popup使用
    chrome.storage.local.set({
      lastSummary: request.summary,
      lastMindmap: request.mindmap,
      lastModel: config.preferredModel
    }, () => {
      sendResponse({status: 'success'});
    });
    return true; // 表示异步响应
  }
});

// 生成摘要和思维导图
async function generateSummary(captions, videoId) {
  try {
    // 组合字幕文本
    const text = captions.map(c => c.text).join(' ');
    if (!text.trim()) throw new Error('字幕为空');
    
    // 检查是否有API密钥
    if (!config.openaiApiKey && !config.geminiApiKey && !config.deepseekApiKey) {
      throw new Error('请先在设置中配置API密钥');
    }
    
    // 加载模板
    const summaryTemplate = config.summaryTemplate || await loadDefaultTemplate('summary');
    const mindmapTemplate = config.mindmapTemplate || await loadDefaultTemplate('mindmap');
    
    // 生成摘要和思维导图
    const [summary, mindmap] = await Promise.all([
      generateContent(summaryTemplate.replace('{transcript}', text), 'summary'),
      generateContent(mindmapTemplate.replace('{transcript}', text), 'mindmap')
    ]);
    
    return { summary, mindmap };
  } catch (error) {
    console.error('生成摘要失败:', error);
    throw error;
  }
}

// 生成内容（摘要或思维导图）
async function generateContent(prompt, type) {
  const model = config.preferredModel;
  
  if (model.startsWith('gpt-') && config.openaiApiKey) {
    return await callOpenAI(prompt, model);
  } else if (model.startsWith('gemini-') && config.geminiApiKey) {
    return await callGemini(prompt, model);
  } else if (model.startsWith('deepseek-') && config.deepseekApiKey) {
    return await callDeepSeek(prompt, model);
  } else {
    // 尝试其他可用的API
    if (config.openaiApiKey) {
      return await callOpenAI(prompt, 'gpt-3.5-turbo');
    } else if (config.geminiApiKey) {
      return await callGemini(prompt, 'gemini-pro');
    } else if (config.deepseekApiKey) {
      return await callDeepSeek(prompt, 'deepseek-chat');
    } else {
      throw new Error('没有可用的API密钥');
    }
  }
}

// OpenAI API调用
async function callOpenAI(prompt, model) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.openaiApiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 2000,
      temperature: 0.7
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`OpenAI API错误: ${error.error?.message || response.statusText}`);
  }
  
  const data = await response.json();
  return data.choices[0].message.content;
}

// Gemini API调用
async function callGemini(prompt, model) {
  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${config.geminiApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        maxOutputTokens: 2000,
        temperature: 0.7
      }
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Gemini API错误: ${error.error?.message || response.statusText}`);
  }
  
  const data = await response.json();
  return data.candidates[0].content.parts[0].text;
}

// DeepSeek API调用
async function callDeepSeek(prompt, model) {
  const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.deepseekApiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 2000,
      temperature: 0.7
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`DeepSeek API错误: ${error.error?.message || response.statusText}`);
  }
  
  const data = await response.json();
  return data.choices[0].message.content;
}

// 加载默认模板
async function loadDefaultTemplate(type) {
  try {
    const response = await fetch(chrome.runtime.getURL(`templates/${type}-template.txt`));
    return await response.text();
  } catch (error) {
    console.error(`加载默认${type}模板失败:`, error);
    return getFallbackTemplate(type);
  }
}

// 备用模板
function getFallbackTemplate(type) {
  if (type === 'summary') {
    return `请为以下视频字幕创建详细摘要：

🎯 核心主题
[视频的主要话题和核心观点]

📝 关键要点
1. [要点1]
2. [要点2]
3. [要点3]

💡 主要论点
* [论点1及其支持证据]
* [论点2及其支持证据]

🔍 深度分析
[对内容的深入解读和分析]

✨ 学习价值
[视频的教育意义和实践价值]

字幕内容：
{transcript}`;
  } else {
    return `请基于以下视频字幕创建一个层次化的思维导图（使用Mermaid语法）：

要求：
1. 使用mindmap语法
2. 包含主要主题和子主题
3. 层次清晰，逻辑合理
4. 突出关键概念

字幕内容：
{transcript}`;
  }
}
