#!/bin/bash

# Firefox插件打包脚本
echo "开始打包Firefox插件..."

# 创建临时目录
TEMP_DIR="firefox-extension-temp"
PACKAGE_NAME="youtube-summary-extension.zip"

# 清理之前的临时文件
rm -rf "$TEMP_DIR"
rm -f "$PACKAGE_NAME"

# 创建临时目录
mkdir -p "$TEMP_DIR"

# 复制必要文件
echo "复制文件..."
cp manifest.json "$TEMP_DIR/"
cp background.js "$TEMP_DIR/"
cp content.js "$TEMP_DIR/"

# 复制popup文件夹
cp -r popup "$TEMP_DIR/"

# 复制settings文件夹
cp -r settings "$TEMP_DIR/"

# 复制templates文件夹
cp -r templates "$TEMP_DIR/"

# 创建简单的图标文件夹（如果不存在）
if [ ! -d "icons" ]; then
    echo "创建图标文件夹..."
    mkdir -p "$TEMP_DIR/icons"
    
    # 创建简单的48x48像素图标（base64编码的PNG）
    echo "创建默认图标..."
    cat > "$TEMP_DIR/icons/icon-48.png.txt" << 'EOF'
这是一个占位符文件。请替换为实际的48x48像素PNG图标。
EOF
    
    cat > "$TEMP_DIR/icons/icon-96.png.txt" << 'EOF'
这是一个占位符文件。请替换为实际的96x96像素PNG图标。
EOF
else
    cp -r icons "$TEMP_DIR/"
fi

# 创建README文件
cat > "$TEMP_DIR/README.md" << 'EOF'
# YouTube智能摘要 Firefox插件

## 功能特性
- 自动生成YouTube视频摘要
- 创建思维导图
- 支持多种AI模型（OpenAI GPT、Google Gemini、DeepSeek）
- 可自定义摘要和思维导图模板

## 安装说明
1. 打开Firefox浏览器
2. 在地址栏输入 `about:debugging`
3. 点击"此Firefox"
4. 点击"临时载入附加组件"
5. 选择manifest.json文件

## 使用方法
1. 在设置页面配置API密钥
2. 访问YouTube视频页面
3. 点击"生成摘要"按钮
4. 查看摘要和思维导图

## 注意事项
- 需要配置至少一个AI服务的API密钥
- 确保视频有可用的字幕
- 图标文件需要替换为实际的PNG文件

## 版本信息
版本: 1.0.0
兼容性: Firefox 109+
EOF

# 进入临时目录并创建zip文件
cd "$TEMP_DIR"
echo "创建zip文件..."
zip -r "../$PACKAGE_NAME" ./*

# 返回原目录
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "生成的文件: $PACKAGE_NAME"
echo ""
echo "安装说明:"
echo "1. 打开Firefox浏览器"
echo "2. 在地址栏输入 about:debugging"
echo "3. 点击'此Firefox'"
echo "4. 点击'临时载入附加组件'"
echo "5. 选择解压后的manifest.json文件"
echo ""
echo "注意: 图标文件需要替换为实际的PNG文件"
