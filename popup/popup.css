* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', system-ui, sans-serif;
  background: #f8f9fa;
  color: #1c1c1e;
  width: 500px;
  min-height: 400px;
  padding: 0;
  font-size: 14px;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

header {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

h1 {
  font-size: 18px;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

.actions button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;
}

.actions button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.tabs {
  display: flex;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.tab {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.tab.active {
  background: white;
  color: #2575fc;
  border-bottom: 2px solid #2575fc;
}

.tab-content {
  flex: 1;
  overflow: auto;
  background: white;
}

.content {
  padding: 16px;
  display: none;
  height: 100%;
}

.content.active {
  display: block;
}

.output {
  white-space: pre-wrap;
  line-height: 1.6;
}

#summary-output {
  padding: 8px 0;
}

#mindmap-container {
  width: 100%;
  height: 300px;
  overflow: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  padding: 16px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #e9ecef;
  border-top: 1px solid #dee2e6;
  font-size: 12px;
  color: #6c757d;
}

#settings-btn {
  background: transparent;
  border: none;
  color: #2575fc;
  cursor: pointer;
  font-weight: 500;
}

/* 摘要样式增强 */
#summary-output h2, 
#summary-output h3 {
  margin-top: 1.2em;
  margin-bottom: 0.6em;
}

#summary-output ul, 
#summary-output ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

#summary-output li {
  margin-bottom: 0.5em;
}

#summary-output .core-theme {
  color: #6a11cb;
  font-weight: bold;
}

#summary-output .key-points {
  color: #2575fc;
}

#summary-output .arguments {
  color: #20c997;
}

#summary-output .analysis {
  color: #fd7e14;
}

#summary-output .value {
  color: #d6336c;
}