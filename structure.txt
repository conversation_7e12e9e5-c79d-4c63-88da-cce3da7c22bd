youtube-summary-extension/
├── manifest.json          # 扩展清单文件
├── background.js          # 后台脚本处理API请求
├── content.js             # 内容脚本注入到YouTube页面
├── popup/
│   ├── popup.html         # 摘要显示弹窗
│   ├── popup.css          # 弹窗样式
│   └── popup.js           # 弹窗交互逻辑
├── settings/
│   ├── settings.html      # 设置页面
│   ├── settings.css       # 设置页面样式
│   └── settings.js        # 设置页面逻辑
├── icons/
│   ├── icon-48.png        # 扩展图标
│   └── icon-96.png        # 高清扩展图标
└── templates/
    ├── summary-template.txt   # 默认摘要模板
    └── mindmap-template.txt    # 默认思维导图模板