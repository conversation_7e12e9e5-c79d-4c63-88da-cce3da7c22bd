// Manifest.json 验证脚本
const fs = require('fs');
const path = require('path');

function validateManifest() {
    try {
        // 读取manifest.json文件
        const manifestPath = path.join(__dirname, 'manifest.json');
        const manifestContent = fs.readFileSync(manifestPath, 'utf8');
        
        // 解析JSON
        const manifest = JSON.parse(manifestContent);
        
        console.log('🔍 验证 Manifest.json...\n');
        
        // 基本字段验证
        const requiredFields = ['manifest_version', 'name', 'version'];
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!manifest[field]) {
                console.log(`❌ 缺少必需字段: ${field}`);
                isValid = false;
            } else {
                console.log(`✅ ${field}: ${manifest[field]}`);
            }
        });
        
        // Manifest版本验证
        if (manifest.manifest_version === 2) {
            console.log('✅ 使用 Manifest V2 (Firefox兼容)');
            
            // V2特定字段验证
            if (manifest.browser_action) {
                console.log('✅ browser_action 配置正确');
            } else if (manifest.action) {
                console.log('⚠️  检测到 action 字段，应使用 browser_action (V2)');
                isValid = false;
            }
            
            // web_accessible_resources格式验证
            if (manifest.web_accessible_resources) {
                if (Array.isArray(manifest.web_accessible_resources)) {
                    const firstResource = manifest.web_accessible_resources[0];
                    if (typeof firstResource === 'string') {
                        console.log('✅ web_accessible_resources 格式正确 (V2)');
                    } else if (typeof firstResource === 'object') {
                        console.log('❌ web_accessible_resources 使用了 V3 格式，应使用字符串数组');
                        isValid = false;
                    }
                } else {
                    console.log('❌ web_accessible_resources 应该是数组');
                    isValid = false;
                }
            }
            
        } else if (manifest.manifest_version === 3) {
            console.log('⚠️  使用 Manifest V3 (Firefox支持有限)');
        }
        
        // 权限验证
        if (manifest.permissions && Array.isArray(manifest.permissions)) {
            console.log(`✅ 权限配置: ${manifest.permissions.length} 项`);
            manifest.permissions.forEach(permission => {
                console.log(`   - ${permission}`);
            });
        }
        
        // 后台脚本验证
        if (manifest.background) {
            if (manifest.background.scripts) {
                console.log('✅ 后台脚本配置 (V2 格式)');
                console.log(`   - 脚本: ${manifest.background.scripts.join(', ')}`);
                console.log(`   - 持久性: ${manifest.background.persistent !== false ? '是' : '否'}`);
            } else if (manifest.background.service_worker) {
                console.log('⚠️  检测到 service_worker，Firefox可能不支持');
            }
        }
        
        // 内容脚本验证
        if (manifest.content_scripts && Array.isArray(manifest.content_scripts)) {
            console.log(`✅ 内容脚本配置: ${manifest.content_scripts.length} 项`);
            manifest.content_scripts.forEach((script, index) => {
                console.log(`   脚本 ${index + 1}:`);
                console.log(`   - 匹配: ${script.matches?.join(', ') || '无'}`);
                console.log(`   - JS文件: ${script.js?.join(', ') || '无'}`);
                console.log(`   - CSS文件: ${script.css?.join(', ') || '无'}`);
            });
        }
        
        console.log('\n' + '='.repeat(50));
        
        if (isValid) {
            console.log('🎉 Manifest.json 验证通过！');
            console.log('✅ 可以在Firefox中安装');
        } else {
            console.log('❌ Manifest.json 存在问题，需要修复');
        }
        
        return isValid;
        
    } catch (error) {
        console.log('❌ 验证失败:', error.message);
        return false;
    }
}

// 文件存在性验证
function validateFiles() {
    console.log('\n🔍 验证文件存在性...\n');
    
    const requiredFiles = [
        'manifest.json',
        'background.js',
        'content.js',
        'popup/popup.html',
        'popup/popup.js',
        'popup/popup.css',
        'settings/settings.html',
        'settings/settings.js',
        'settings/settings.css',
        'templates/summary-template.txt',
        'templates/mindmap-template.txt'
    ];
    
    let allFilesExist = true;
    
    requiredFiles.forEach(file => {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
            console.log(`✅ ${file}`);
        } else {
            console.log(`❌ ${file} (缺失)`);
            allFilesExist = false;
        }
    });
    
    return allFilesExist;
}

// 主函数
function main() {
    console.log('🚀 Firefox插件验证工具\n');
    
    const manifestValid = validateManifest();
    const filesValid = validateFiles();
    
    console.log('\n' + '='.repeat(50));
    console.log('📋 验证总结:');
    console.log(`Manifest.json: ${manifestValid ? '✅ 通过' : '❌ 失败'}`);
    console.log(`文件完整性: ${filesValid ? '✅ 通过' : '❌ 失败'}`);
    
    if (manifestValid && filesValid) {
        console.log('\n🎉 插件准备就绪，可以安装！');
        console.log('\n📝 安装步骤:');
        console.log('1. 在Firefox中访问 about:debugging');
        console.log('2. 点击"此Firefox"');
        console.log('3. 点击"临时载入附加组件"');
        console.log('4. 选择 manifest.json 文件');
    } else {
        console.log('\n⚠️  请修复上述问题后重新验证');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = { validateManifest, validateFiles };
