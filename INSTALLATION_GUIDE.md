# YouTube智能摘要 Firefox插件安装指南

## 📦 插件包内容

生成的 `youtube-summary-extension.zip` 包含以下文件：
- `manifest.json` - 插件清单文件
- `service-worker.js` - 后台服务脚本
- `content.js` - 内容脚本（注入到YouTube页面）
- `popup/` - 弹窗界面文件
- `settings/` - 设置页面文件
- `templates/` - 默认模板文件
- `icons/` - 图标文件（包含SVG格式）

## 🚀 安装步骤

### 方法一：临时安装（开发测试）

1. **解压插件包**
   ```bash
   unzip youtube-summary-extension.zip
   ```

2. **打开Firefox开发者模式**
   - 在Firefox地址栏输入：`about:debugging`
   - 点击左侧的"此Firefox"

3. **加载插件**
   - 点击"临时载入附加组件"按钮
   - 选择解压后文件夹中的 `manifest.json` 文件
   - 插件将立即加载并显示在扩展列表中

### 方法二：打包安装（推荐）

1. **准备签名**
   - 访问 [Firefox Add-ons Developer Hub](https://addons.mozilla.org/developers/)
   - 注册开发者账号
   - 上传zip文件进行签名

2. **安装签名后的插件**
   - 下载签名后的.xpi文件
   - 在Firefox中打开该文件
   - 确认安装

## ⚙️ 配置设置

### 1. 获取API密钥

插件支持三种AI服务，至少需要配置一个：

**OpenAI GPT**
- 访问：https://platform.openai.com/api-keys
- 创建新的API密钥
- 复制密钥备用

**Google Gemini**
- 访问：https://makersuite.google.com/app/apikey
- 创建API密钥
- 复制密钥备用

**DeepSeek**
- 访问：https://platform.deepseek.com/api_keys
- 创建API密钥
- 复制密钥备用

### 2. 配置插件

1. **打开设置页面**
   - 点击Firefox工具栏中的插件图标
   - 点击弹窗底部的"设置"按钮
   - 或者右键插件图标选择"选项"

2. **填写API密钥**
   - 在对应字段中粘贴API密钥
   - 选择首选模型
   - 点击"保存设置"

3. **自定义模板（可选）**
   - 切换到"摘要模板"或"思维导图模板"标签
   - 编辑模板内容
   - 确保包含 `{transcript}` 占位符
   - 保存设置

## 🎯 使用方法

1. **访问YouTube视频**
   - 打开任意YouTube视频页面
   - 确保视频有可用字幕

2. **生成摘要**
   - 在视频标题下方找到"生成摘要"按钮
   - 点击按钮开始生成
   - 等待处理完成

3. **查看结果**
   - 点击工具栏中的插件图标
   - 在弹窗中查看摘要和思维导图
   - 可以导出为文本或图片

## 🔧 故障排除

### 常见问题

**1. 按钮不显示**
- 确保在YouTube视频页面
- 刷新页面重试
- 检查插件是否正确加载

**2. 无法获取字幕**
- 确保视频有可用字幕
- 尝试开启自动生成字幕
- 检查视频是否为私有或受限

**3. API调用失败**
- 检查API密钥是否正确
- 确认API服务可用
- 检查网络连接

**4. 生成结果为空**
- 检查模板是否包含 `{transcript}` 占位符
- 尝试使用默认模板
- 检查字幕内容是否有效

### 开发者调试

1. **查看控制台日志**
   - 按F12打开开发者工具
   - 查看Console标签中的错误信息

2. **检查插件状态**
   - 访问 `about:debugging`
   - 查看插件是否正常运行
   - 检查错误信息

## 📝 注意事项

1. **API费用**
   - 使用AI服务会产生费用
   - 建议设置使用限额
   - 定期检查API使用情况

2. **隐私安全**
   - API密钥仅存储在本地
   - 视频字幕会发送到AI服务
   - 不会收集个人信息

3. **兼容性**
   - 支持Firefox 109+
   - 需要启用JavaScript
   - 某些企业网络可能阻止API调用

## 🆕 更新说明

当有新版本时：
1. 下载新的插件包
2. 在 `about:debugging` 中移除旧版本
3. 按照安装步骤重新安装
4. 重新配置设置（如果需要）

## 📞 技术支持

如遇到问题，请检查：
1. Firefox版本是否支持
2. 网络连接是否正常
3. API密钥是否有效
4. 插件权限是否正确

---

**版本**: 1.0.0  
**兼容性**: Firefox 109+  
**最后更新**: 2025-06-21
