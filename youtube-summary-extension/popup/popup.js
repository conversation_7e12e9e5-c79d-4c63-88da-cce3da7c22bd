document.addEventListener('DOMContentLoaded', function() {
  // 初始化Mermaid
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: { useMaxWidth: false }
  });

  // 获取当前标签页的YouTube视频信息
  chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
    let videoTitle = 'YouTube视频';

    try {
      if (tabs[0] && tabs[0].url.includes('youtube.com/watch')) {
        const title = tabs[0].title.replace(' - YouTube', '');
        if (title) videoTitle = title;
      }
    } catch (e) {
      console.error('获取视频标题失败:', e);
    }

    // 从存储加载上次的摘要数据
    chrome.storage.local.get([
      'lastSummary',
      'lastMindmap',
      'lastModel'
    ], function(result) {
      const { lastSummary, lastMindmap, lastModel } = result;

      // 显示模型信息
      const modelInfo = document.getElementById('model-info');
      if (lastModel) {
        modelInfo.textContent = `使用: ${lastModel.replace(/-/g, ' ').toUpperCase()}`;
      }

      // 显示摘要内容
      const summaryOutput = document.getElementById('summary-output');
      if (lastSummary) {
        summaryOutput.innerHTML = formatSummary(lastSummary);
      } else {
        summaryOutput.textContent = '没有可用的摘要数据。请先在YouTube视频页面点击"生成摘要"按钮。';
      }

      // 显示思维导图
      const mindmapOutput = document.getElementById('mindmap-output');
      if (lastMindmap) {
        mindmapOutput.innerHTML = lastMindmap;
        try {
          mermaid.run({
            querySelector: '#mindmap-output',
          }).then(() => {
            console.log('思维导图渲染成功');
          }).catch((error) => {
            console.error('渲染思维导图失败:', error);
            mindmapOutput.innerHTML = `<pre>${lastMindmap}</pre><p class="error">渲染思维导图失败，显示原始数据。</p>`;
          });
        } catch (error) {
          console.error('渲染思维导图失败:', error);
          mindmapOutput.innerHTML = `<pre>${lastMindmap}</pre><p class="error">渲染思维导图失败，显示原始数据。</p>`;
        }
      } else {
        mindmapOutput.textContent = '没有可用的思维导图数据。';
      }
  
  // 标签切换功能
  const tabs = document.querySelectorAll('.tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // 更新标签状态
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      
      // 显示对应内容
      const tabName = tab.getAttribute('data-tab');
      document.querySelectorAll('.content').forEach(content => {
        content.classList.remove('active');
      });
      document.getElementById(`${tabName}-content`).classList.add('active');
      
      // 如果是思维导图标签，重新渲染
      if (tabName === 'mindmap' && lastMindmap) {
        setTimeout(() => {
          mermaid.run({
            querySelector: '#mindmap-output',
          });
        }, 100);
      }
    });
  });
  
  // 导出文本功能
  document.getElementById('export-text').addEventListener('click', () => {
    if (!lastSummary) return;
    
    const blob = new Blob([`视频标题: ${videoTitle}\n\n${lastSummary}`], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    chrome.downloads.download({
      url: url,
      filename: `YouTube摘要_${sanitizeFilename(videoTitle)}.txt`,
      saveAs: true
    });
  });
  
  // 导出图片功能 (思维导图)
  document.getElementById('export-image').addEventListener('click', async () => {
    if (!lastMindmap) return;
    
    try {
      // 获取SVG元素
      const svg = document.querySelector('#mindmap-output svg');
      if (!svg) throw new Error('没有可导出的SVG内容');
      
      // 克隆SVG以避免修改原始元素
      const clonedSvg = svg.cloneNode(true);
      clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      
      // 添加背景色
      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('width', '100%');
      rect.setAttribute('height', '100%');
      rect.setAttribute('fill', '#ffffff');
      clonedSvg.insertBefore(rect, clonedSvg.firstChild);
      
      // 转换为数据URL
      const svgData = new XMLSerializer().serializeToString(clonedSvg);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // 设置画布大小
      const img = new Image();
      img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
      
      await new Promise((resolve) => {
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          resolve();
        };
      });
      
      // 触发下载
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        chrome.downloads.download({
          url: url,
          filename: `YouTube思维导图_${sanitizeFilename(videoTitle)}.png`,
          saveAs: true
        });
      }, 'image/png');
      
    } catch (error) {
      console.error('导出图片失败:', error);
      alert('导出图片失败: ' + error.message);
    }
  });
  
      // 打开设置页面
      document.getElementById('settings-btn').addEventListener('click', () => {
        chrome.runtime.openOptionsPage();
      });
    });
  });
});

// 格式化摘要文本
function formatSummary(text) {
  // 将Markdown风格的标题转换为HTML
  let html = text
    .replace(/^🎯 (.*$)/gm, '<h2 class="core-theme">🎯 $1</h2>')
    .replace(/^📝 (.*$)/gm, '<h2 class="key-points">📝 $1</h2>')
    .replace(/^💡 (.*$)/gm, '<h2 class="arguments">💡 $1</h2>')
    .replace(/^🔍 (.*$)/gm, '<h2 class="analysis">🔍 $1</h2>')
    .replace(/^✨ (.*$)/gm, '<h2 class="value">✨ $1</h2>')
    .replace(/^\* (.*$)/gm, '<li>$1</li>')
    .replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>')
    .replace(/\n/g, '<br>');
  
  // 将无序列表和有序列表包裹在ul/ol中
  html = html.replace(/<li>.*?<\/li>/g, (match) => {
    return `<ul>${match}</ul>`;
  });
  
  // 合并相邻的ul/ol
  html = html.replace(/<\/ul><br><ul>/g, '');
  
  return html;
}

// 清理文件名中的非法字符
function sanitizeFilename(filename) {
  return filename.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
}