* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', system-ui, sans-serif;
  background: #f8f9fa;
  color: #1c1c1e;
  min-width: 600px;
  padding: 0;
  font-size: 14px;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

header {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

header h1 {
  font-size: 18px;
  font-weight: 600;
}

header img {
  width: 24px;
  height: 24px;
}

main {
  flex: 1;
  padding: 20px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.settings-section h2 {
  font-size: 16px;
  margin-bottom: 16px;
  color: #343a40;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2575fc;
  box-shadow: 0 0 0 3px rgba(37, 117, 252, 0.1);
}

.form-group small {
  display: block;
  margin-top: 6px;
  color: #6c757d;
  font-size: 12px;
}

.form-group a {
  color: #2575fc;
  text-decoration: none;
}

.form-group a:hover {
  text-decoration: underline;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 16px;
}

.tabs .tab {
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  position: relative;
}

.tabs .tab.active {
  color: #2575fc;
}

.tabs .tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #2575fc;
}

.template-content {
  display: none;
}

.template-content.active {
  display: block;
}

.template-content textarea {
  width: 100%;
  height: 300px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
}

.template-info {
  margin-top: 8px;
  color: #6c757d;
  font-size: 13px;
}

.template-info p {
  margin-bottom: 8px;
}

.template-info code {
  background: #e9ecef;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
}

.reset-btn {
  background: none;
  border: none;
  color: #2575fc;
  cursor: pointer;
  font-size: 13px;
  padding: 0;
  margin-top: 8px;
}

.reset-btn:hover {
  text-decoration: underline;
}

footer {
  padding: 16px 20px;
  background: #f1f3f5;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#save-settings {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

#save-settings:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

#save-settings:active {
  transform: translateY(0);
}

#status-message {
  font-size: 13px;
  color: #20c997;
  opacity: 0;
  transition: opacity 0.3s;
}

#status-message.visible {
  opacity: 1;
}

#status-message.error {
  color: #dc3545;
}