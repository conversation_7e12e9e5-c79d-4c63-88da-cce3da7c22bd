<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 96 96">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6a11cb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2575fc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="48" cy="48" r="44" fill="url(#grad1)" stroke="#fff" stroke-width="2"/>
  
  <!-- YouTube播放按钮 -->
  <polygon points="36,32 36,64 64,48" fill="white" opacity="0.9"/>
  
  <!-- 文档图标 -->
  <rect x="20" y="20" width="24" height="32" rx="2" fill="white" opacity="0.7"/>
  <rect x="22" y="26" width="20" height="2" fill="url(#grad1)"/>
  <rect x="22" y="30" width="16" height="2" fill="url(#grad1)"/>
  <rect x="22" y="34" width="18" height="2" fill="url(#grad1)"/>
  <rect x="22" y="38" width="14" height="2" fill="url(#grad1)"/>
  
  <!-- AI/智能图标 -->
  <circle cx="72" cy="72" r="8" fill="white" opacity="0.9"/>
  <circle cx="72" cy="72" r="4" fill="url(#grad1)"/>
  <circle cx="68" cy="68" r="2" fill="white"/>
  <circle cx="76" cy="68" r="2" fill="white"/>
  <circle cx="72" cy="76" r="2" fill="white"/>
</svg>
