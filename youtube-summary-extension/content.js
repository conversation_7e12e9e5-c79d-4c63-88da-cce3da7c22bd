// Content Script for YouTube Summary Extension
// 添加摘要按钮到YouTube页面
function addSummaryButton() {
  // 检查是否已存在按钮
  if (document.getElementById('summary-extension-btn')) return;

  const buttonContainer = document.querySelector('#top-row.style-scope.ytd-watch-metadata');
  if (!buttonContainer) return;

  const button = document.createElement('button');
  button.id = 'summary-extension-btn';
  button.innerHTML = `
    <span style="display:flex;align-items:center;gap:6px;">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
        <polyline points="14 2 14 8 20 8"></polyline>
        <line x1="16" y1="13" x2="8" y2="13"></line>
        <line x1="16" y1="17" x2="8" y2="17"></line>
        <polyline points="10 9 9 9 8 9"></polyline>
      </svg>
      生成摘要
    </span>
  `;
  button.style = `
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(106, 17, 203, 0.3);
  `;

  button.addEventListener('mouseenter', () => {
    button.style.transform = 'translateY(-1px)';
    button.style.boxShadow = '0 4px 12px rgba(106, 17, 203, 0.4)';
  });

  button.addEventListener('mouseleave', () => {
    button.style.transform = 'translateY(0)';
    button.style.boxShadow = '0 2px 8px rgba(106, 17, 203, 0.3)';
  });

  button.addEventListener('click', generateSummary);
  buttonContainer.appendChild(button);
}

// 生成摘要
async function generateSummary() {
  const btn = document.getElementById('summary-extension-btn');
  const originalText = btn.innerHTML;

  try {
    // 显示加载状态
    btn.innerHTML = '加载中...';
    btn.disabled = true;

    // 获取视频ID和字幕
    const videoId = new URLSearchParams(window.location.search).get('v');
    const captions = await getVideoCaptions(videoId);

    if (!captions || captions.length === 0) {
      throw new Error('无法获取视频字幕。请确保字幕已启用。');
    }

    // 发送字幕到后台处理
    const response = await chrome.runtime.sendMessage({
      action: 'generateSummary',
      captions: captions,
      videoId: videoId
    });

    if (response.error) {
      throw new Error(response.error);
    }

    // 打开弹出窗口显示结果
    chrome.runtime.sendMessage({action: 'showSummary', summary: response.summary, mindmap: response.mindmap});

  } catch (error) {
    console.error('生成摘要失败:', error);
    alert('生成摘要失败: ' + error.message);
  } finally {
    btn.innerHTML = originalText;
    btn.disabled = false;
  }
}

// 获取视频字幕
async function getVideoCaptions(videoId) {
  return new Promise((resolve) => {
    // 尝试从页面获取字幕
    const captions = [];
    const captionElements = document.querySelectorAll('.ytp-caption-segment');

    if (captionElements.length > 0) {
      captionElements.forEach((element, index) => {
        captions.push({
          text: element.textContent,
          start: index * 2, // 估算时间
          end: (index + 1) * 2
        });
      });
      resolve(captions);
    } else {
      // 尝试通过YouTube API获取字幕
      try {
        const player = document.querySelector('#movie_player');
        if (player && player.getPlayerState) {
          player.getVideoData().then(videoData => {
            const language = navigator.language.split('-')[0] || 'en';
            player.getAvailableTrackLanguages().then(languages => {
              const targetLang = languages.includes(language) ? language :
                               languages.includes('en') ? 'en' : languages[0];
              player.getTextTracks().then(tracks => {
                const track = tracks.find(t => t.language === targetLang);
                if (track) {
                  track.getCues().then(cues => {
                    resolve(cues.map(cue => ({
                      text: cue.text,
                      start: cue.startTime,
                      end: cue.endTime
                    })));
                  });
                } else {
                  resolve([]);
                }
              });
            });
          });
        } else {
          resolve([]);
        }
      } catch (error) {
        console.error('获取字幕失败:', error);
        resolve([]);
      }
    }
  });
}

// 初始化 - 监听URL变化并添加按钮
let lastUrl = location.href;
new MutationObserver(() => {
  const url = location.href;
  if (url !== lastUrl && url.includes('/watch')) {
    lastUrl = url;
    setTimeout(addSummaryButton, 1000);
  }
}).observe(document, {subtree: true, childList: true});

// 初始添加按钮
if (window.location.href.includes('/watch')) {
  setTimeout(addSummaryButton, 2000);
}