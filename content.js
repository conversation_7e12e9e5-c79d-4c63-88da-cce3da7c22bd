// 存储配置
let config = {
  openaiApiKey: '',
  geminiApiKey: '',
  deepseekApiKey: '',
  preferredModel: 'gpt-3.5-turbo',
  summaryTemplate: '',
  mindmapTemplate: ''
};

// 从存储中加载配置
chrome.storage.sync.get([
  'openaiApiKey', 
  'geminiApiKey', 
  'deepseekApiKey', 
  'preferredModel',
  'summaryTemplate',
  'mindmapTemplate'
], (result) => {
  Object.assign(config, result);
});

// 监听消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'generateSummary') {
    generateSummary(request.captions, request.videoId)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({error: error.message}));
    return true; // 表示异步响应
  }
  
  if (request.action === 'saveConfig') {
    Object.assign(config, request.config);
    chrome.storage.sync.set(request.config);
    sendResponse({status: 'success'});
  }
});

// 生成摘要和思维导图
async function generateSummary(captions, videoId) {
  try {
    // 组合字幕文本
    const text = captions.map(c => c.text).join(' ');
    if (!text.trim()) throw new Error('字幕为空');
    
    // 检查是否有API密钥
    if (!config.openaiApiKey && !config.geminiApiKey && !config.deepseekApiKey) {
      throw new Error('请先在设置中配置API密钥');
    }
    
    // 应用模板
    const summaryTemplate = config.summaryTemplate || await loadDefaultTemplate('summary');
    const mindmapTemplate = config.mindmapTemplate || await loadDefaultTemplate('mindmap');
    
    const summaryPrompt = summaryTemplate.replace('{transcript}', text.substring(0, 10000));
    const mindmapPrompt = mindmapTemplate.replace('{transcript}', text.substring(0, 10000));
    
    // 根据偏好选择模型
    let summary, mindmap;
    
    if (config.preferredModel.startsWith('gpt') && config.openaiApiKey) {
      summary = await callOpenAI(summaryPrompt, config.openaiApiKey, config.preferredModel);
      mindmap = await callOpenAI(mindmapPrompt, config.openaiApiKey, config.preferredModel);
    } 
    else if (config.preferredModel.startsWith('gemini') && config.geminiApiKey) {
      summary = await callGemini(summaryPrompt, config.geminiApiKey, config.preferredModel);
      mindmap = await callGemini(mindmapPrompt, config.geminiApiKey, config.preferredModel);
    }
    else if (config.preferredModel.startsWith('deepseek') && config.deepseekApiKey) {
      summary = await callDeepSeek(summaryPrompt, config.deepseekApiKey, config.preferredModel);
      mindmap = await callDeepSeek(mindmapPrompt, config.deepseekApiKey, config.preferredModel);
    }
    else {
      // 尝试任何可用的API
      if (config.openaiApiKey) {
        summary = await callOpenAI(summaryPrompt, config.openaiApiKey, 'gpt-3.5-turbo');
        mindmap = await callOpenAI(mindmapPrompt, config.openaiApiKey, 'gpt-3.5-turbo');
      } 
      else if (config.geminiApiKey) {
        summary = await callGemini(summaryPrompt, config.geminiApiKey, 'gemini-pro');
        mindmap = await callGemini(mindmapPrompt, config.geminiApiKey, 'gemini-pro');
      }
      else if (config.deepseekApiKey) {
        summary = await callDeepSeek(summaryPrompt, config.deepseekApiKey, 'deepseek-chat');
        mindmap = await callDeepSeek(mindmapPrompt, config.deepseekApiKey, 'deepseek-chat');
      }
      else {
        throw new Error('没有可用的API密钥');
      }
    }
    
    return { summary, mindmap };
    
  } catch (error) {
    console.error('后台生成摘要失败:', error);
    return { error: error.message };
  }
}

// 调用OpenAI API
async function callOpenAI(prompt, apiKey, model) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 1000
    })
  });
  
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error?.message || 'OpenAI API错误');
  }
  
  return data.choices[0].message.content.trim();
}

// 调用Gemini API
async function callGemini(prompt, apiKey, model) {
  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 1000
      }
    })
  });
  
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error?.message || 'Gemini API错误');
  }
  
  return data.candidates[0].content.parts[0].text.trim();
}

// 调用DeepSeek API
async function callDeepSeek(prompt, apiKey, model) {
  const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 1000
    })
  });
  
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error?.message || 'DeepSeek API错误');
  }
  
  return data.choices[0].message.content.trim();
}

// 加载默认模板
async function loadDefaultTemplate(type) {
  try {
    const response = await fetch(chrome.runtime.getURL(`templates/${type}-template.txt`));
    return await response.text();
  } catch (error) {
    console.error(`加载默认${type}模板失败:`, error);
    return getFallbackTemplate(type);
  }
}

// 备用模板
function getFallbackTemplate(type) {
  if (type === 'summary') {
    return `请为以下视频字幕创建详细摘要：

🎯 核心主题
[视频的主要话题和核心观点]

📝 关键要点
1. [要点1]
2. [要点2]
3. [要点3]
[继续列出所有重要要点]

💡 主要论点
* [论点1及其支持证据]
* [论点2及其支持证据]
* [论点3及其支持证据]

🔍 深度分析
[对内容的深入解读和分析]

✨ 学习价值
[视频的教育意义和实践价值]

确保摘要全面且准确，突出核心价值。

字幕内容：
{transcript}`;
  } else {
    return `请基于以下视频字幕创建一个层次化的思维导图（使用Mermaid语法）：

格式要求：
- 使用Mermaid的mindmap图表语法
- 中心主题为视频标题
- 主要分支为核心概念
- 子分支为关键细节和支持证据
- 使用简洁的短语（不超过5个词）

输出示例：
\`\`\`mermaid
mindmap
  root((视频标题))
    核心概念1
      关键细节1
        支持证据1
        支持证据2
    核心概念2
      关键细节2
      关键细节3
\`\`\`

请根据以下字幕内容创建思维导图：

{transcript}`;
  }
}