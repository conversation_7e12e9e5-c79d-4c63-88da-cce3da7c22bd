<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>扩展设置</title>
  <link rel="stylesheet" href="settings.css">
  <script src="settings.js" defer></script>
</head>
<body>
  <div class="container">
    <header>
      <h1><img src="/icons/icon-48.png" alt="图标"> YouTube智能摘要设置</h1>
    </header>
    
    <main>
      <section class="settings-section">
        <h2>API 配置</h2>
        
        <div class="form-group">
          <label for="openai-api-key">OpenAI API 密钥</label>
          <input type="password" id="openai-api-key" placeholder="sk-...">
          <small>从 <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI平台</a> 获取</small>
        </div>
        
        <div class="form-group">
          <label for="gemini-api-key">Gemini API 密钥</label>
          <input type="password" id="gemini-api-key" placeholder="AIza...">
          <small>从 <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a> 获取</small>
        </div>
        
        <div class="form-group">
          <label for="deepseek-api-key">DeepSeek API 密钥</label>
          <input type="password" id="deepseek-api-key" placeholder="sk-...">
          <small>从 <a href="https://platform.deepseek.com/api-keys" target="_blank">DeepSeek平台</a> 获取</small>
        </div>
        
        <div class="form-group">
          <label for="preferred-model">首选模型</label>
          <select id="preferred-model">
            <option value="gpt-3.5-turbo">OpenAI GPT-3.5 Turbo</option>
            <option value="gpt-4-turbo">OpenAI GPT-4 Turbo</option>
            <option value="gemini-pro">Google Gemini Pro</option>
            <option value="deepseek-chat">DeepSeek Chat</option>
          </select>
        </div>
      </section>
      
      <section class="settings-section">
        <h2>提示模板</h2>
        
        <div class="tabs">
          <button class="tab active" data-tab="summary-template">摘要模板</button>
          <button class="tab" data-tab="mindmap-template">思维导图模板</button>
        </div>
        
        <div class="tab-content">
          <div id="summary-template" class="template-content active">
            <textarea id="summary-template-input" placeholder="输入摘要模板..."></textarea>
            <div class="template-info">
              <p>使用 <code>{transcript}</code> 占位符表示字幕内容</p>
              <button class="reset-btn" data-type="summary">重置为默认</button>
            </div>
          </div>
          
          <div id="mindmap-template" class="template-content">
            <textarea id="mindmap-template-input" placeholder="输入思维导图模板..."></textarea>
            <div class="template-info">
              <p>使用 <code>{transcript}</code> 占位符表示字幕内容</p>
              <p>思维导图输出应为Mermaid语法格式</p>
              <button class="reset-btn" data-type="mindmap">重置为默认</button>
            </div>
          </div>
        </div>
      </section>
    </main>
    
    <footer>
      <button id="save-settings">保存设置</button>
      <div id="status-message"></div>
    </footer>
  </div>
</body>
</html>