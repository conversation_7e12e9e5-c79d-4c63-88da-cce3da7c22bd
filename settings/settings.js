document.addEventListener('DOMContentLoaded', async function() {
  // 从存储加载配置
  const config = await loadConfig();
  
  // 初始化表单值
  document.getElementById('openai-api-key').value = config.openaiApiKey || '';
  document.getElementById('gemini-api-key').value = config.geminiApiKey || '';
  document.getElementById('deepseek-api-key').value = config.deepseekApiKey || '';
  document.getElementById('preferred-model').value = config.preferredModel || 'gpt-3.5-turbo';
  
  // 加载模板
  const { summaryTemplate, mindmapTemplate } = await loadTemplates();
  document.getElementById('summary-template-input').value = summaryTemplate;
  document.getElementById('mindmap-template-input').value = mindmapTemplate;
  
  // 标签切换功能
  const tabs = document.querySelectorAll('.tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // 更新标签状态
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      
      // 显示对应内容
      const tabName = tab.getAttribute('data-tab');
      document.querySelectorAll('.template-content').forEach(content => {
        content.classList.remove('active');
      });
      document.getElementById(tabName).classList.add('active');
    });
  });
  
  // 重置模板按钮
  document.querySelectorAll('.reset-btn').forEach(btn => {
    btn.addEventListener('click', async function() {
      const type = this.getAttribute('data-type');
      const defaultTemplate = await getDefaultTemplate(type);
      
      if (type === 'summary') {
        document.getElementById('summary-template-input').value = defaultTemplate;
      } else {
        document.getElementById('mindmap-template-input').value = defaultTemplate;
      }
      
      showStatus('模板已重置为默认值');
    });
  });
  
  // 保存设置
  document.getElementById('save-settings').addEventListener('click', async function() {
    const saveBtn = this;
    saveBtn.disabled = true;
    saveBtn.textContent = '保存中...';
    
    try {
      const newConfig = {
        openaiApiKey: document.getElementById('openai-api-key').value.trim(),
        geminiApiKey: document.getElementById('gemini-api-key').value.trim(),
        deepseekApiKey: document.getElementById('deepseek-api-key').value.trim(),
        preferredModel: document.getElementById('preferred-model').value,
        summaryTemplate: document.getElementById('summary-template-input').value.trim(),
        mindmapTemplate: document.getElementById('mindmap-template-input').value.trim()
      };
      
      // 验证至少有一个API密钥
      if (!newConfig.openaiApiKey && !newConfig.geminiApiKey && !newConfig.deepseekApiKey) {
        throw new Error('至少需要配置一个API密钥');
      }
      
      // 验证模板是否包含{transcript}占位符
      if (!newConfig.summaryTemplate.includes('{transcript}')) {
        throw new Error('摘要模板必须包含 {transcript} 占位符');
      }
      
      if (!newConfig.mindmapTemplate.includes('{transcript}')) {
        throw new Error('思维导图模板必须包含 {transcript} 占位符');
      }
      
      // 保存到存储
      await chrome.storage.sync.set(newConfig);
      
      // 通知后台脚本更新配置
      chrome.runtime.sendMessage({
        action: 'saveConfig',
        config: newConfig
      });
      
      showStatus('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      showStatus(error.message, true);
    } finally {
      saveBtn.disabled = false;
      saveBtn.textContent = '保存设置';
    }
  });
  
  // 加载配置
  async function loadConfig() {
    const result = await chrome.storage.sync.get([
      'openaiApiKey',
      'geminiApiKey',
      'deepseekApiKey',
      'preferredModel',
      'summaryTemplate',
      'mindmapTemplate'
    ]);
    
    return {
      openaiApiKey: result.openaiApiKey || '',
      geminiApiKey: result.geminiApiKey || '',
      deepseekApiKey: result.deepseekApiKey || '',
      preferredModel: result.preferredModel || 'gpt-3.5-turbo',
      summaryTemplate: result.summaryTemplate || '',
      mindmapTemplate: result.mindmapTemplate || ''
    };
  }
  
  // 加载模板
  async function loadTemplates() {
    const config = await loadConfig();
    
    return {
      summaryTemplate: config.summaryTemplate || await getDefaultTemplate('summary'),
      mindmapTemplate: config.mindmapTemplate || await getDefaultTemplate('mindmap')
    };
  }
  
  // 获取默认模板
  async function getDefaultTemplate(type) {
    try {
      const response = await fetch(chrome.runtime.getURL(`templates/${type}-template.txt`));
      return await response.text();
    } catch (error) {
      console.error(`加载默认${type}模板失败:`, error);
      return getFallbackTemplate(type);
    }
  }
  
  // 备用模板
  function getFallbackTemplate(type) {
    if (type === 'summary') {
      return `请为以下视频字幕创建详细摘要：

🎯 核心主题
[视频的主要话题和核心观点]

📝 关键要点
1. [要点1]
2. [要点2]
3. [要点3]
[继续列出所有重要要点]

💡 主要论点
* [论点1及其支持证据]
* [论点2及其支持证据]
* [论点3及其支持证据]

🔍 深度分析
[对内容的深入解读和分析]

✨ 学习价值
[视频的教育意义和实践价值]

确保摘要全面且准确，突出核心价值。

字幕内容：
{transcript}`;
    } else {
      return `请基于以下视频字幕创建一个层次化的思维导图（使用Mermaid语法）：

格式要求：
- 使用Mermaid的mindmap图表语法
- 中心主题为视频标题
- 主要分支为核心概念
- 子分支为关键细节和支持证据
- 使用简洁的短语（不超过5个词）

输出示例：
\`\`\`mermaid
mindmap
  root((视频标题))
    核心概念1
      关键细节1
        支持证据1
        支持证据2
    核心概念2
      关键细节2
      关键细节3
\`\`\`

请根据以下字幕内容创建思维导图：

{transcript}`;
    }
  }
  
  // 显示状态消息
  function showStatus(message, isError = false) {
    const statusEl = document.getElementById('status-message');
    statusEl.textContent = message;
    statusEl.classList.toggle('error', isError);
    statusEl.classList.add('visible');
    
    setTimeout(() => {
      statusEl.classList.remove('visible');
    }, 3000);
  }
});