<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox插件测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            opacity: 0.9;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            color: #666;
            font-weight: bold;
        }
        .checklist li.checked:before {
            content: "✅ ";
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>🔧 Firefox插件测试页面</h1>
    
    <div class="test-section">
        <h2>📋 安装验证清单</h2>
        <ul class="checklist">
            <li id="check-1">解压插件包到本地目录</li>
            <li id="check-2">在Firefox中访问 <span class="code">about:debugging</span></li>
            <li id="check-3">点击"此Firefox"</li>
            <li id="check-4">点击"临时载入附加组件"</li>
            <li id="check-5">选择 <span class="code">manifest.json</span> 文件</li>
            <li id="check-6">插件出现在扩展列表中</li>
        </ul>
        <button onclick="markChecked('check-1')">标记完成</button>
    </div>

    <div class="test-section">
        <h2>🎯 功能测试</h2>
        
        <h3>1. 基础功能测试</h3>
        <ul class="checklist">
            <li id="func-1">插件图标显示在工具栏</li>
            <li id="func-2">点击图标打开弹窗</li>
            <li id="func-3">弹窗显示正常（无错误）</li>
            <li id="func-4">设置按钮可以点击</li>
            <li id="func-5">设置页面正常打开</li>
        </ul>
        
        <h3>2. YouTube页面测试</h3>
        <ul class="checklist">
            <li id="youtube-1">访问任意YouTube视频页面</li>
            <li id="youtube-2">"生成摘要"按钮出现在视频标题下方</li>
            <li id="youtube-3">按钮样式正常显示</li>
            <li id="youtube-4">鼠标悬停有动画效果</li>
        </ul>
        
        <h3>3. API配置测试</h3>
        <ul class="checklist">
            <li id="api-1">可以输入API密钥</li>
            <li id="api-2">可以选择首选模型</li>
            <li id="api-3">设置可以保存</li>
            <li id="api-4">保存后显示成功消息</li>
        </ul>
    </div>

    <div class="test-section warning">
        <h2>⚠️ 常见问题排查</h2>
        
        <h3>问题1: 插件无法加载</h3>
        <p><strong>解决方案:</strong></p>
        <ul>
            <li>确保选择的是 <span class="code">manifest.json</span> 文件</li>
            <li>检查文件路径是否正确</li>
            <li>确保所有文件都已解压</li>
        </ul>
        
        <h3>问题2: 按钮不显示</h3>
        <p><strong>解决方案:</strong></p>
        <ul>
            <li>刷新YouTube页面</li>
            <li>确保在视频播放页面（包含 /watch 的URL）</li>
            <li>等待页面完全加载</li>
        </ul>
        
        <h3>问题3: 弹窗显示错误</h3>
        <p><strong>解决方案:</strong></p>
        <ul>
            <li>按F12打开开发者工具查看错误</li>
            <li>检查控制台是否有JavaScript错误</li>
            <li>重新加载插件</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🚀 完整测试流程</h2>
        <ol>
            <li><strong>安装插件</strong> - 按照上述清单完成安装</li>
            <li><strong>配置API</strong> - 在设置页面添加至少一个API密钥</li>
            <li><strong>访问YouTube</strong> - 打开任意有字幕的视频</li>
            <li><strong>生成摘要</strong> - 点击"生成摘要"按钮</li>
            <li><strong>查看结果</strong> - 在弹窗中查看摘要和思维导图</li>
            <li><strong>测试导出</strong> - 尝试导出文本和图片</li>
        </ol>
    </div>

    <div class="test-section success">
        <h2>✅ 测试完成</h2>
        <p>如果所有测试项目都通过，说明插件安装和配置成功！</p>
        <p><strong>下一步:</strong></p>
        <ul>
            <li>开始使用插件生成YouTube视频摘要</li>
            <li>根据需要自定义模板</li>
            <li>探索不同AI模型的效果</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📞 技术支持</h2>
        <p>如果遇到问题，请检查:</p>
        <ul>
            <li><strong>Firefox版本:</strong> 确保使用Firefox 109+</li>
            <li><strong>网络连接:</strong> 确保可以访问AI服务API</li>
            <li><strong>API密钥:</strong> 确保密钥有效且有足够额度</li>
            <li><strong>视频字幕:</strong> 确保YouTube视频有可用字幕</li>
        </ul>
    </div>

    <script>
        function markChecked(id) {
            const element = document.getElementById(id);
            if (element) {
                element.classList.add('checked');
            }
        }

        // 自动检测Firefox版本
        function checkFirefoxVersion() {
            const userAgent = navigator.userAgent;
            const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
            
            if (firefoxMatch) {
                const version = parseInt(firefoxMatch[1]);
                const versionInfo = document.createElement('div');
                versionInfo.className = version >= 109 ? 'test-section success' : 'test-section error';
                versionInfo.innerHTML = `
                    <h3>🌐 浏览器版本检测</h3>
                    <p>检测到Firefox版本: <strong>${version}</strong></p>
                    <p>${version >= 109 ? '✅ 版本兼容' : '❌ 需要Firefox 109或更高版本'}</p>
                `;
                document.body.insertBefore(versionInfo, document.body.firstChild.nextSibling);
            }
        }

        // 页面加载时执行检测
        document.addEventListener('DOMContentLoaded', checkFirefoxVersion);
    </script>
</body>
</html>
