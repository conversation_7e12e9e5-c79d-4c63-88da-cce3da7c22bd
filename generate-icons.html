<!DOCTYPE html>
<html>
<head>
    <title>Generate Icons</title>
</head>
<body>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <canvas id="canvas96" width="96" height="96"></canvas>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 创建渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#6a11cb');
            gradient.addColorStop(1, '#2575fc');
            
            // 背景圆形
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 白色边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // YouTube播放按钮
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.moveTo(size * 0.375, size * 0.33);
            ctx.lineTo(size * 0.375, size * 0.67);
            ctx.lineTo(size * 0.67, size * 0.5);
            ctx.closePath();
            ctx.fill();
            
            // 文档图标
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.fillRect(size * 0.21, size * 0.21, size * 0.25, size * 0.33);
            
            // 文档线条
            ctx.fillStyle = gradient;
            ctx.fillRect(size * 0.23, size * 0.27, size * 0.21, size * 0.02);
            ctx.fillRect(size * 0.23, size * 0.31, size * 0.17, size * 0.02);
            ctx.fillRect(size * 0.23, size * 0.35, size * 0.19, size * 0.02);
            ctx.fillRect(size * 0.23, size * 0.39, size * 0.15, size * 0.02);
            
            // AI图标
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.arc(size * 0.75, size * 0.75, size * 0.08, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size * 0.75, size * 0.75, size * 0.04, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        // 生成图标
        drawIcon(document.getElementById('canvas48'), 48);
        drawIcon(document.getElementById('canvas96'), 96);
        
        // 下载功能
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 自动下载
        setTimeout(() => {
            downloadCanvas(document.getElementById('canvas48'), 'icon-48.png');
            downloadCanvas(document.getElementById('canvas96'), 'icon-96.png');
        }, 1000);
    </script>
</body>
</html>
